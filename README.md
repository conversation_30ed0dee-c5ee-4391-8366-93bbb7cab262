# Manipal Business Solutions - reKYC Digital Assistant

A modern web-based digital assistant for customer reKYC (Re-Know Your Customer) process with secure address update functionality, powered by Manipal Business Solutions.

## Features

- 🏢 **Manipal Business Solutions Branding** - Professional corporate interface
- 🤖 **Interactive Digital Assistant** - Hybrid chat and form-based interface
- 📱 **Mobile OTP Verification** - Secure SMS-based authentication
- 🆔 **Aadhaar Integration** - Government-verified address updates
- ✅ **Customer Consent Management** - Compliant consent workflow
- 📋 **Structured Data Entry** - Professional form fields for accurate data capture
- 🎨 **Modern, Responsive Design** - Works seamlessly on all devices
- 🔄 **Process Restart Capability** - Easy workflow restart functionality

## How to Use

1. Open `index.html` in your web browser
2. Type "start" to begin the reKYC process
3. Follow the bot's instructions step by step

## Process Flow

### 1. Customer Identification
- Enter Customer ID
- Provide registered mobile number
- Verify with OTP

### 2. Customer Details Display
- View current customer information
- Choose whether to update address

### 3. Address Update (Optional)
- If address update is required:
  - Enter <PERSON> number
  - Verify with <PERSON><PERSON><PERSON><PERSON> OTP
  - Confirm updated address details
- If no address update:
  - Provide consent to proceed

### 4. Final Confirmation
- Review all details
- Provide final consent
- Complete the process

## Demo Credentials

For testing purposes, the application generates and displays OTPs automatically:

- **Customer Name**: KAMAL KISHOR GUPTA (pre-configured)
- **Any valid 10-digit mobile number** (starting with 6-9)
- **Any valid 12-digit Aadhaar number**
- **OTPs are displayed in the chat** for demo purposes

## Technical Details

### Files Structure
```
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── bot.js             # Bot logic and state management
├── app.js             # Main application logic
└── README.md          # This file
```

### Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- No external dependencies required

### Keyboard Shortcuts
- **Enter**: Send message
- **Escape**: Clear input field
- **Ctrl/Cmd + R**: Restart process (when focused on page)

## Customization

### Modifying Customer Data
Edit the `handleOtp()` method in `bot.js` to customize default customer information:

```javascript
// Simulate fetching customer details
this.customerData.name = "Your Name";
this.customerData.address = "Your Address";
```

### Styling Changes
Modify `styles.css` to change colors, fonts, or layout:

```css
/* Change primary color */
:root {
    --primary-color: #4facfe;
    --secondary-color: #00f2fe;
}
```

### Adding New States
1. Add new state to `BotState` enum in `bot.js`
2. Add case in `processMessage()` method
3. Implement handler method
4. Update `getStatusText()` method

## Security Notes

⚠️ **This is a demo application**:
- OTPs are generated client-side for demonstration
- No real SMS/Aadhaar integration
- Customer data is not persisted
- Not suitable for production use without proper backend integration

## Production Considerations

For production deployment:
1. Implement secure backend API
2. Integrate with real SMS gateway
3. Connect to Aadhaar verification service
4. Add proper data encryption and storage
5. Implement session management
6. Add comprehensive error handling
7. Include audit logging

## Browser Console Commands

For testing and debugging:
```javascript
// Restart the bot
testBot.restart()

// Check current state
testBot.getState()

// View customer data
testBot.getCustomerData()
```

## License

This is a demonstration project. Please ensure compliance with relevant regulations when implementing similar systems in production.
