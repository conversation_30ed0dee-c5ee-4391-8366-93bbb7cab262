// Main Application Logic
class ChatApp {
    constructor() {
        this.bot = new ReKYCBot();
        this.chatMessages = document.getElementById('chatMessages');
        this.userInput = document.getElementById('userInput');
        this.sendButton = document.getElementById('sendButton');
        this.statusText = document.getElementById('statusText');
        this.dataEntryForm = document.getElementById('dataEntryForm');
        this.chatInputContainer = document.getElementById('chatInputContainer');
        this.quickActions = document.getElementById('quickActions');

        this.initializeEventListeners();
        this.startChat();
    }

    initializeEventListeners() {
        // Send button click
        this.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });

        // Enter key press
        this.userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // Auto-focus input
        this.userInput.focus();
    }

    startChat() {
        // Add welcome message with typing effect
        setTimeout(() => {
            this.addBotMessage("👋 Hello! I'm your MBS reKYC Digital Assistant.");
        }, 500);

        setTimeout(() => {
            this.addBotMessage("I'm here to help you update your customer details securely and efficiently.");
        }, 1500);

        setTimeout(() => {
            this.addBotMessage("Type 'start' when you're ready to begin the reKYC process! 🚀");
            this.showQuickActions();
        }, 2500);

        this.updateStatus();
    }

    sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        // Add user message to chat
        this.addUserMessage(message);
        
        // Clear input
        this.userInput.value = '';
        
        // Disable input temporarily
        this.setInputEnabled(false);
        
        // Show typing indicator
        this.showTypingIndicator();
        
        // Process message after a short delay (simulate processing time)
        setTimeout(() => {
            this.hideTypingIndicator();
            
            let response;
            if (message.toLowerCase() === 'start' && this.bot.getCurrentState() === BotState.INIT) {
                response = this.bot.processMessage('start');
            } else if (message.toLowerCase() === 'restart') {
                this.bot.reset();
                response = "🔄 Process restarted. Type 'start' to begin again.";
            } else {
                response = this.bot.processMessage(message);
            }
            
            this.addBotMessage(response);
            this.updateStatus();
            this.checkForFormDisplay();
            this.setInputEnabled(true);
            this.userInput.focus();
        }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
    }

    checkForFormDisplay() {
        const currentState = this.bot.getCurrentState();

        // Show form for specific states that require structured data entry
        if (currentState === BotState.CUSTOMER_ID_INPUT) {
            this.showCustomerIdForm();
        } else if (currentState === BotState.MOBILE_INPUT) {
            this.showMobileForm();
        } else if (currentState === BotState.OTP_VALIDATION) {
            this.showOtpForm();
        } else if (currentState === BotState.AADHAAR_INPUT) {
            this.showAadhaarForm();
        } else if (currentState === BotState.AADHAAR_OTP) {
            this.showAadhaarOtpForm();
        } else {
            this.hideForm();
        }
    }

    showCustomerIdForm() {
        this.dataEntryForm.innerHTML = `
            <div class="form-group">
                <label for="customerIdInput">Customer ID *</label>
                <input type="text" id="customerIdInput" placeholder="Enter your Customer ID" required>
                <div class="help-text">Enter your unique customer identification number</div>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn btn-primary" onclick="chatApp.submitCustomerId()">Submit</button>
            </div>
        `;
        this.showForm();
    }

    showMobileForm() {
        this.dataEntryForm.innerHTML = `
            <div class="form-group">
                <label for="mobileInput">Registered Mobile Number *</label>
                <input type="tel" id="mobileInput" placeholder="Enter 10-digit mobile number" pattern="[6-9][0-9]{9}" required>
                <div class="help-text">Enter your registered mobile number (10 digits starting with 6-9)</div>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn btn-primary" onclick="chatApp.submitMobile()">Send OTP</button>
            </div>
        `;
        this.showForm();
    }

    showOtpForm() {
        this.dataEntryForm.innerHTML = `
            <div class="form-group">
                <label for="otpInput">Enter OTP *</label>
                <input type="text" id="otpInput" placeholder="Enter 6-digit OTP" pattern="[0-9]{6}" maxlength="6" required>
                <div class="help-text">Enter the 6-digit OTP sent to your mobile number</div>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn btn-primary" onclick="chatApp.submitOtp()">Verify OTP</button>
            </div>
        `;
        this.showForm();
    }

    showAadhaarForm() {
        this.dataEntryForm.innerHTML = `
            <div class="form-group">
                <label for="aadhaarInput">Aadhaar Number *</label>
                <input type="text" id="aadhaarInput" placeholder="Enter 12-digit Aadhaar number" pattern="[0-9]{12}" maxlength="12" required>
                <div class="help-text">Enter your 12-digit Aadhaar number for address verification</div>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn btn-primary" onclick="chatApp.submitAadhaar()">Verify Aadhaar</button>
            </div>
        `;
        this.showForm();
    }

    showAadhaarOtpForm() {
        this.dataEntryForm.innerHTML = `
            <div class="form-group">
                <label for="aadhaarOtpInput">Enter Aadhaar OTP *</label>
                <input type="text" id="aadhaarOtpInput" placeholder="Enter 6-digit OTP" pattern="[0-9]{6}" maxlength="6" required>
                <div class="help-text">Enter the 6-digit OTP sent to your Aadhaar registered mobile</div>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn btn-primary" onclick="chatApp.submitAadhaarOtp()">Verify OTP</button>
            </div>
        `;
        this.showForm();
    }

    showForm() {
        this.dataEntryForm.style.display = 'block';
        this.chatInputContainer.style.display = 'none';
        this.hideQuickActions();
    }

    hideForm() {
        this.dataEntryForm.style.display = 'none';
        this.chatInputContainer.style.display = 'flex';

        // Show quick actions for certain states
        const currentState = this.bot.getCurrentState();
        if (currentState === BotState.ADDRESS_CHOICE || currentState === BotState.FINAL_CONSENT) {
            this.showQuickActions();
        }
    }

    // Form submission methods
    submitCustomerId() {
        const input = document.getElementById('customerIdInput');
        const value = input.value.trim();
        if (value) {
            this.processFormSubmission(value);
        } else {
            this.showFormError(input, 'Customer ID is required');
        }
    }

    submitMobile() {
        const input = document.getElementById('mobileInput');
        const value = input.value.trim();
        if (value && /^[6-9]\d{9}$/.test(value)) {
            this.processFormSubmission(value);
        } else {
            this.showFormError(input, 'Please enter a valid 10-digit mobile number starting with 6-9');
        }
    }

    submitOtp() {
        const input = document.getElementById('otpInput');
        const value = input.value.trim();
        if (value && /^\d{6}$/.test(value)) {
            this.processFormSubmission(value);
        } else {
            this.showFormError(input, 'Please enter a valid 6-digit OTP');
        }
    }

    submitAadhaar() {
        const input = document.getElementById('aadhaarInput');
        const value = input.value.trim();
        if (value && /^\d{12}$/.test(value)) {
            this.processFormSubmission(value);
        } else {
            this.showFormError(input, 'Please enter a valid 12-digit Aadhaar number');
        }
    }

    submitAadhaarOtp() {
        const input = document.getElementById('aadhaarOtpInput');
        const value = input.value.trim();
        if (value && /^\d{6}$/.test(value)) {
            this.processFormSubmission(value);
        } else {
            this.showFormError(input, 'Please enter a valid 6-digit OTP');
        }
    }

    processFormSubmission(value) {
        // Add user message to show what was submitted
        this.addUserMessage(value);

        // Hide form and show processing
        this.hideForm();
        this.setInputEnabled(false);
        this.showTypingIndicator();

        // Process the submission
        setTimeout(() => {
            this.hideTypingIndicator();
            const response = this.bot.processMessage(value);
            this.addBotMessage(response);
            this.updateStatus();
            this.checkForFormDisplay();
            this.setInputEnabled(true);
            this.userInput.focus();
        }, 1000 + Math.random() * 1000);
    }

    showFormError(input, message) {
        // Remove existing error
        const existingError = input.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Add error styling
        input.style.borderColor = '#dc3545';

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.color = '#dc3545';
        errorDiv.style.fontSize = '0.8rem';
        errorDiv.style.marginTop = '5px';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);

        // Focus the input
        input.focus();

        // Remove error styling after user starts typing
        input.addEventListener('input', function() {
            input.style.borderColor = '';
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, { once: true });
    }

    // Quick action methods
    showQuickActions() {
        this.quickActions.style.display = 'block';
    }

    hideQuickActions() {
        this.quickActions.style.display = 'none';
    }

    sendQuickMessage(message) {
        this.userInput.value = message;
        this.sendMessage();
    }

    addUserMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        messageDiv.textContent = message;

        // Add timestamp to user message
        const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = timestamp;
        messageDiv.appendChild(timeDiv);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addBotMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot-message';

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        // Handle special formatting for customer details
        if (message.includes('Customer Details Retrieved:') || message.includes('Address Updated Successfully!')) {
            messageDiv.innerHTML = this.formatSpecialMessage(message);
        } else {
            messageDiv.textContent = message;
        }

        // Add timestamp to message
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = timestamp;
        messageDiv.appendChild(timeDiv);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatSpecialMessage(message) {
        // Add special styling for customer details and success messages
        if (message.includes('Customer Details Retrieved:')) {
            return `<div class="customer-details">${message}</div>`;
        } else if (message.includes('Aadhaar Verification Successful!')) {
            return `<div class="aadhaar-details">${message}</div>`;
        } else if (message.includes('Address Updated Successfully!') || message.includes('reKYC process completed successfully!')) {
            return `<div class="success-message">${message}</div>`;
        }
        return message;
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-indicator';
        typingDiv.id = 'typingIndicator';
        typingDiv.innerHTML = `
            <span>Bot is typing</span>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;
        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    setInputEnabled(enabled) {
        this.userInput.disabled = !enabled;
        this.sendButton.disabled = !enabled;
        
        if (enabled) {
            this.userInput.placeholder = "Type your message...";
        } else {
            this.userInput.placeholder = "Processing...";
        }
    }

    updateStatus() {
        this.statusText.textContent = this.bot.getStatusText();
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    // Utility method to clear chat (for testing)
    clearChat() {
        this.chatMessages.innerHTML = '';
        this.bot.reset();
        this.startChat();
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
    
    // Add some helpful keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + R to restart
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            window.chatApp.bot.reset();
            window.chatApp.clearChat();
        }
        
        // Escape to clear input
        if (e.key === 'Escape') {
            window.chatApp.userInput.value = '';
            window.chatApp.userInput.focus();
        }
    });
});

// Add some helpful console commands for testing
window.testBot = {
    restart: () => {
        window.chatApp.bot.reset();
        window.chatApp.clearChat();
        console.log('Bot restarted');
    },
    
    getState: () => {
        console.log('Current state:', window.chatApp.bot.getCurrentState());
        return window.chatApp.bot.getCurrentState();
    },
    
    getCustomerData: () => {
        console.log('Customer data:', window.chatApp.bot.customerData);
        return window.chatApp.bot.customerData;
    }
};
