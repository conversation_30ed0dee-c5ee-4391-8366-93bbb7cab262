// Bot States
const BotState = {
    INIT: 'init',
    CUSTOMER_ID_INPUT: 'customer_id_input',
    MOBILE_INPUT: 'mobile_input',
    OTP_VALIDATION: 'otp_validation',
    CUSTOMER_DETAILS: 'customer_details',
    ADDRESS_CHOICE: 'address_choice',
    AADHAAR_INPUT: 'aadhaar_input',
    AADHAAR_OTP: 'aadhaar_otp',
    FINAL_CONSENT: 'final_consent',
    COMPLETED: 'completed'
};

// Customer Data Structure
class CustomerData {
    constructor() {
        this.customerId = '';
        this.mobile = '';
        this.name = '';
        this.address = '';
        this.aadhaar = '';
        this.otp = '';
        this.aadhaarOtp = '';
    }
}

// Main Bot Class
class ReKYCBot {
    constructor() {
        this.state = BotState.INIT;
        this.customerData = new CustomerData();
        this.generatedOtp = null;
        this.generatedAadhaarOtp = null;
    }

    processMessage(userInput) {
        switch (this.state) {
            case BotState.INIT:
                return this.startProcess();
            case BotState.CUSTOMER_ID_INPUT:
                return this.handleCustomerId(userInput);
            case BotState.MOBILE_INPUT:
                return this.handleMobile(userInput);
            case BotState.OTP_VALIDATION:
                return this.handleOtp(userInput);
            case BotState.CUSTOMER_DETAILS:
                return this.showCustomerDetails();
            case BotState.ADDRESS_CHOICE:
                return this.handleAddressChoice(userInput);
            case BotState.AADHAAR_INPUT:
                return this.handleAadhaar(userInput);
            case BotState.AADHAAR_OTP:
                return this.handleAadhaarOtp(userInput);
            case BotState.FINAL_CONSENT:
                return this.handleFinalConsent(userInput);
            default:
                return "Process completed. Thank you!";
        }
    }

    startProcess() {
        this.state = BotState.CUSTOMER_ID_INPUT;
        return "Welcome to Manipal Business Solutions reKYC Digital Assistant! 🏦\n\n🌟 India's largest tech-led business solutions ecosystem for the BFSI industry\n\nI'll help you update your customer details securely with UIDAI verified information.\n\nPlease enter your Customer ID:";
    }

    handleCustomerId(customerId) {
        if (!customerId.trim()) {
            return "Customer ID is required. Please enter your Customer ID:";
        }

        this.customerData.customerId = customerId.trim();
        this.state = BotState.MOBILE_INPUT;
        return "Please enter your registered mobile number:";
    }

    handleMobile(mobile) {
        if (!this.validateMobile(mobile)) {
            return "Invalid mobile number. Please enter a valid 10-digit mobile number:";
        }

        this.customerData.mobile = mobile.trim();
        this.generatedOtp = this.generateOtp();
        this.state = BotState.OTP_VALIDATION;

        return `OTP has been sent to ${mobile}. 📱\nPlease enter the 6-digit OTP:\n\n🔐 For demo purposes, your OTP is: ${this.generatedOtp}`;
    }

    handleOtp(otp) {
        if (otp.trim() !== this.generatedOtp) {
            return "Invalid OTP. Please enter the correct 6-digit OTP:";
        }

        // Fetch customer details from Manipal Business Solutions database
        this.customerData.name = "KAMAL KISHOR GUPTA";
        this.customerData.address = "123 MG Road, Manipal, Udupi District, Karnataka 576104";

        this.state = BotState.CUSTOMER_DETAILS;
        return this.showCustomerDetails();
    }

    showCustomerDetails() {
        this.state = BotState.ADDRESS_CHOICE;
        return `✅ Customer Details Retrieved:\n\n📋 Name: ${this.customerData.name}\n🆔 Customer ID: ${this.customerData.customerId}\n📱 Mobile: ${this.customerData.mobile}\n🏠 Address: ${this.customerData.address}\n\nDo you need to update your address?\nReply with 'yes' or 'no'`;
    }

    handleAddressChoice(choice) {
        const normalizedChoice = choice.toLowerCase().trim();

        if (['yes', 'y'].includes(normalizedChoice)) {
            this.state = BotState.AADHAAR_INPUT;
            return "To update your address, Aadhaar verification is required.\n\nPlease enter your Aadhaar number (12 digits):";
        } else if (['no', 'n'].includes(normalizedChoice)) {
            this.state = BotState.FINAL_CONSENT;
            return "Do you consent to proceed with the current details? (yes/no)";
        } else {
            return "Please reply with 'yes' or 'no'";
        }
    }

    handleAadhaar(aadhaar) {
        if (!this.validateAadhaar(aadhaar)) {
            return "Invalid Aadhaar number. Please enter a valid 12-digit Aadhaar number:";
        }

        this.customerData.aadhaar = aadhaar.trim();
        this.generatedAadhaarOtp = this.generateOtp();
        this.state = BotState.AADHAAR_OTP;

        return `OTP has been sent to your Aadhaar registered mobile. 📱\nPlease enter the 6-digit OTP:\n\n🔐 For demo purposes, your Aadhaar OTP is: ${this.generatedAadhaarOtp}`;
    }

    handleAadhaarOtp(otp) {
        if (otp.trim() !== this.generatedAadhaarOtp) {
            return "Invalid OTP. Please enter the correct 6-digit OTP:";
        }

        // Simulate fetching Aadhaar details from UIDAI
        const aadhaarDetails = this.fetchAadhaarDetailsFromUIDIA();

        // Update customer data with UIDAI details
        this.customerData.address = aadhaarDetails.address;
        this.customerData.aadhaarDetails = aadhaarDetails;

        this.state = BotState.FINAL_CONSENT;

        return `✅ Aadhaar Verification Successful!\n\n📋 UIDAI Verified Details:\n\n👤 Name: ${aadhaarDetails.name}\n🆔 Aadhaar: ${aadhaarDetails.maskedAadhaar}\n📅 DOB: ${aadhaarDetails.dob}\n👤 Gender: ${aadhaarDetails.gender}\n📱 Mobile: ${aadhaarDetails.mobile}\n🏠 Address: ${aadhaarDetails.address}\n\n✅ Address has been updated with UIDAI verified information.\n\nDo you consent to proceed with these updated details? (yes/no)`;
    }

    fetchAadhaarDetailsFromUIDIA() {
        // Simulate UIDAI API response with realistic data
        return {
            name: "KAMAL KISHOR GUPTA",
            maskedAadhaar: "XXXX-XXXX-" + this.customerData.aadhaar.slice(-4),
            dob: "15/08/1985",
            gender: "Male",
            mobile: "+91-" + this.customerData.mobile.slice(0, 2) + "XXXXXX" + this.customerData.mobile.slice(-2),
            address: "456 Aadhaar Verified Address, Manipal Technology Park, Bangalore, Karnataka 560066",
            verificationTimestamp: new Date().toISOString(),
            uidaiRefNumber: "REF" + Math.random().toString(36).substr(2, 9).toUpperCase()
        };
    }

    handleFinalConsent(consent) {
        const normalizedConsent = consent.toLowerCase().trim();

        if (['yes', 'y'].includes(normalizedConsent)) {
            this.state = BotState.COMPLETED;
            return "🎉 reKYC process completed successfully!\nThank you for updating your details.\n\n✅ Your information has been securely updated in our system.";
        } else if (['no', 'n'].includes(normalizedConsent)) {
            this.state = BotState.INIT;
            return "❌ Process cancelled. You can restart the reKYC process anytime by typing 'start'.";
        } else {
            return "Please reply with 'yes' or 'no'";
        }
    }

    validateMobile(mobile) {
        return /^[6-9]\d{9}$/.test(mobile.trim());
    }

    validateAadhaar(aadhaar) {
        return /^\d{12}$/.test(aadhaar.trim());
    }

    generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    getCurrentState() {
        return this.state;
    }

    getStatusText() {
        switch (this.state) {
            case BotState.INIT:
                return "Ready to start";
            case BotState.CUSTOMER_ID_INPUT:
                return "Waiting for Customer ID";
            case BotState.MOBILE_INPUT:
                return "Waiting for mobile number";
            case BotState.OTP_VALIDATION:
                return "Waiting for OTP verification";
            case BotState.CUSTOMER_DETAILS:
                return "Displaying customer details";
            case BotState.ADDRESS_CHOICE:
                return "Waiting for address update choice";
            case BotState.AADHAAR_INPUT:
                return "Waiting for Aadhaar number";
            case BotState.AADHAAR_OTP:
                return "Waiting for Aadhaar OTP";
            case BotState.FINAL_CONSENT:
                return "Waiting for final consent";
            case BotState.COMPLETED:
                return "Process completed";
            default:
                return "Ready";
        }
    }

    reset() {
        this.state = BotState.INIT;
        this.customerData = new CustomerData();
        this.generatedOtp = null;
        this.generatedAadhaarOtp = null;
    }
}
