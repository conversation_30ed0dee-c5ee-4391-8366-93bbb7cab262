<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reKYC Bot - Customer Details Update</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="chat-header">
            <div class="chatbot-header">
                <div class="bot-avatar">
                    <img src="https://cdn.sanity.io/images/rgew5gg8/production/9fcb38ebccb7b39ed975fdacc025e33843ed4fcd-256x256.png" alt="MBS Logo" class="mbs-official-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="mbs-fallback-logo" style="display: none;">
                        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="mbsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#DE2427;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#F6B402;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle cx="50" cy="50" r="45" fill="white"/>
                            <circle cx="50" cy="50" r="40" fill="url(#mbsGradient)"/>
                            <text x="50" y="58" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">MBS</text>
                        </svg>
                    </div>
                </div>
                <div class="bot-info">
                    <h2>MBS ReKYC Setu</h2>
                    <div class="bot-status">
                        <span class="status-dot"></span>
                        <span>Online • Ready to help</span>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-btn" onclick="chatApp.clearChat()" title="Restart Chat">
                        🔄
                    </button>
                </div>
            </div>
            <div class="company-tagline">
                <p>🏦 Manipal Business Solutions • India's largest tech-led BFSI ecosystem</p>
            </div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be dynamically added here -->
            </div>
        </div>
        
        <div class="input-container">
            <div class="quick-actions" id="quickActions" style="display: none;">
                <div class="quick-action-buttons">
                    <button class="quick-btn" onclick="chatApp.sendQuickMessage('start')">🚀 Start reKYC</button>
                    <button class="quick-btn" onclick="chatApp.sendQuickMessage('yes')">✅ Yes</button>
                    <button class="quick-btn" onclick="chatApp.sendQuickMessage('no')">❌ No</button>
                    <button class="quick-btn" onclick="chatApp.sendQuickMessage('restart')">🔄 Restart</button>
                </div>
            </div>
            <div class="data-entry-form" id="dataEntryForm" style="display: none;">
                <!-- Dynamic form fields will be inserted here -->
            </div>
            <div class="chat-input-container" id="chatInputContainer">
                <input type="text" id="userInput" placeholder="Type your message..." autocomplete="off">
                <button id="sendButton">Send</button>
            </div>
        </div>
        
        <div class="status-bar">
            <span id="statusText">Ready to start</span>
        </div>
    </div>

    <script src="bot.js"></script>
    <script src="app.js"></script>
</body>
</html>
