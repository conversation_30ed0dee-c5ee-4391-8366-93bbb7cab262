<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reKYC Bot - Customer Details Update</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="chat-header">
            <div class="mbs-logo">
                <div class="logo-container">
                    <div class="mbs-icon">MBS</div>
                </div>
                <div class="company-info">
                    <h1>Manipal Business Solutions</h1>
                    <p>reKYC Digital Assistant</p>
                </div>
            </div>
            <div class="service-description">
                <p>India's largest tech-led business solutions ecosystem for the BFSI industry</p>
            </div>
            <div class="trust-indicators">
                <span class="trust-item">🏦 Trusted by Banks</span>
                <span class="trust-item">🔒 Secure & Compliant</span>
                <span class="trust-item">🚀 Technology Driven</span>
            </div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be dynamically added here -->
            </div>
        </div>
        
        <div class="input-container">
            <div class="data-entry-form" id="dataEntryForm" style="display: none;">
                <!-- Dynamic form fields will be inserted here -->
            </div>
            <div class="chat-input-container" id="chatInputContainer">
                <input type="text" id="userInput" placeholder="Type your message..." autocomplete="off">
                <button id="sendButton">Send</button>
            </div>
        </div>
        
        <div class="status-bar">
            <span id="statusText">Ready to start</span>
        </div>
    </div>

    <script src="bot.js"></script>
    <script src="app.js"></script>
</body>
</html>
