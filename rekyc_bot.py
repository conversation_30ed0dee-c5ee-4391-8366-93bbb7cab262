import re
import random
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, Any

class BotState(Enum):
    INIT = "init"
    CUSTOMER_ID_INPUT = "customer_id_input"
    MOBILE_INPUT = "mobile_input"
    OTP_VALIDATION = "otp_validation"
    CUSTOMER_DETAILS = "customer_details"
    ADDRESS_CHOICE = "address_choice"
    AADHAAR_INPUT = "aadhaar_input"
    AADHAAR_OTP = "aadhaar_otp"
    FINAL_CONSENT = "final_consent"
    COMPLETED = "completed"

@dataclass
class CustomerData:
    customer_id: str = ""
    mobile: str = ""
    name: str = ""
    address: str = ""
    aadhaar: str = ""
    otp: str = ""
    aadhaar_otp: str = ""

class ReKYCBot:
    def __init__(self):
        self.state = BotState.INIT
        self.customer_data = CustomerData()
        self.generated_otp = None
        self.generated_aadhaar_otp = None
    
    def process_message(self, user_input: str) -> str:
        if self.state == BotState.INIT:
            return self._start_process()
        elif self.state == BotState.CUSTOMER_ID_INPUT:
            return self._handle_customer_id(user_input)
        elif self.state == BotState.MOBILE_INPUT:
            return self._handle_mobile(user_input)
        elif self.state == BotState.OTP_VALIDATION:
            return self._handle_otp(user_input)
        elif self.state == BotState.CUSTOMER_DETAILS:
            return self._show_customer_details()
        elif self.state == BotState.ADDRESS_CHOICE:
            return self._handle_address_choice(user_input)
        elif self.state == BotState.AADHAAR_INPUT:
            return self._handle_aadhaar(user_input)
        elif self.state == BotState.AADHAAR_OTP:
            return self._handle_aadhaar_otp(user_input)
        elif self.state == BotState.FINAL_CONSENT:
            return self._handle_final_consent(user_input)
        else:
            return "Process completed. Thank you!"
    
    def _start_process(self) -> str:
        self.state = BotState.CUSTOMER_ID_INPUT
        return "Welcome to reKYC process!\n\nPlease enter your Customer ID:"
    
    def _handle_customer_id(self, customer_id: str) -> str:
        if not customer_id.strip():
            return "Customer ID is required. Please enter your Customer ID:"
        
        self.customer_data.customer_id = customer_id.strip()
        self.state = BotState.MOBILE_INPUT
        return "Please enter your registered mobile number:"
    
    def _handle_mobile(self, mobile: str) -> str:
        if not self._validate_mobile(mobile):
            return "Invalid mobile number. Please enter a valid 10-digit mobile number:"
        
        self.customer_data.mobile = mobile.strip()
        self.generated_otp = self._generate_otp()
        self.state = BotState.OTP_VALIDATION
        
        return f"OTP has been sent to {mobile}.\nPlease enter the 6-digit OTP:"
    
    def _handle_otp(self, otp: str) -> str:
        if otp.strip() != self.generated_otp:
            return "Invalid OTP. Please enter the correct 6-digit OTP:"
        
        # Simulate fetching customer details
        self.customer_data.name = "John Doe"
        self.customer_data.address = "123 Main St, City, State"
        
        self.state = BotState.CUSTOMER_DETAILS
        return self._show_customer_details()
    
    def _show_customer_details(self) -> str:
        self.state = BotState.ADDRESS_CHOICE
        return f"""Customer Details:
Name: {self.customer_data.name}
Customer ID: {self.customer_data.customer_id}
Mobile: {self.customer_data.mobile}
Address: {self.customer_data.address}

Do you need to update your address?
Reply with 'yes' or 'no'"""
    
    def _handle_address_choice(self, choice: str) -> str:
        choice = choice.lower().strip()
        
        if choice in ['yes', 'y']:
            self.state = BotState.AADHAAR_INPUT
            return "Please enter your Aadhaar number (12 digits):"
        elif choice in ['no', 'n']:
            self.state = BotState.FINAL_CONSENT
            return "Do you consent to proceed with the current details? (yes/no)"
        else:
            return "Please reply with 'yes' or 'no'"
    
    def _handle_aadhaar(self, aadhaar: str) -> str:
        if not self._validate_aadhaar(aadhaar):
            return "Invalid Aadhaar number. Please enter a valid 12-digit Aadhaar number:"
        
        self.customer_data.aadhaar = aadhaar.strip()
        self.generated_aadhaar_otp = self._generate_otp()
        self.state = BotState.AADHAAR_OTP
        
        return f"OTP has been sent to your Aadhaar registered mobile.\nPlease enter the 6-digit OTP:"
    
    def _handle_aadhaar_otp(self, otp: str) -> str:
        if otp.strip() != self.generated_aadhaar_otp:
            return "Invalid OTP. Please enter the correct 6-digit OTP:"
        
        # Simulate updated address
        self.customer_data.address = "456 New Address, Updated City, State"
        self.state = BotState.FINAL_CONSENT
        
        return f"""Address Updated Successfully!
New Address: {self.customer_data.address}

Do you consent to proceed with these updated details? (yes/no)"""
    
    def _handle_final_consent(self, consent: str) -> str:
        consent = consent.lower().strip()
        
        if consent in ['yes', 'y']:
            self.state = BotState.COMPLETED
            return "✅ reKYC process completed successfully!\nThank you for updating your details."
        elif consent in ['no', 'n']:
            self.state = BotState.INIT
            return "Process cancelled. You can restart the reKYC process anytime."
        else:
            return "Please reply with 'yes' or 'no'"
    
    def _validate_mobile(self, mobile: str) -> bool:
        return bool(re.match(r'^[6-9]\d{9}$', mobile.strip()))
    
    def _validate_aadhaar(self, aadhaar: str) -> bool:
        return bool(re.match(r'^\d{12}$', aadhaar.strip()))
    
    def _generate_otp(self) -> str:
        return str(random.randint(100000, 999999))

# Usage example
if __name__ == "__main__":
    bot = ReKYCBot()
    
    print(bot.process_message("start"))
    # Simulate conversation flow