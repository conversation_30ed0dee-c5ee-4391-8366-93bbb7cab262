* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.chat-header h1 {
    font-size: 2rem;
    margin-bottom: 5px;
}

.chat-header p {
    opacity: 0.9;
    font-size: 0.9rem;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.bot-message {
    background: #e3f2fd;
    color: #1565c0;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.user-message {
    background: #4facfe;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.input-container {
    display: flex;
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

#userInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    font-size: 16px;
    transition: border-color 0.3s;
}

#userInput:focus {
    border-color: #4facfe;
}

#sendButton {
    margin-left: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status-bar {
    background: #f5f5f5;
    padding: 10px 20px;
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.customer-details {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #4facfe;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-line;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.1);
    position: relative;
}

.customer-details::before {
    content: "📋";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
    position: relative;
}

.success-message::before {
    content: "✅";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
    position: relative;
}

.error-message::before {
    content: "❌";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.otp-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 8px 0;
    font-weight: bold;
    color: #856404;
    display: inline-block;
}

.restart-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.restart-hint.show {
    opacity: 1;
}

@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 95vh;
        border-radius: 10px;
    }
    
    .chat-header h1 {
        font-size: 1.5rem;
    }
    
    .message {
        max-width: 90%;
    }
}
