* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>pins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 50%, #438ECC 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 100%);
    color: white;
    padding: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.chatbot-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 15px;
}

.bot-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.mbs-official-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.mbs-fallback-logo {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mbs-fallback-logo svg {
    width: 100%;
    height: 100%;
}

.bot-info {
    flex: 1;
}

.bot-info h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
}

.bot-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    font-size: 0.85rem;
    color: #cbd5e0;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.company-tagline {
    background: rgba(255, 255, 255, 0.05);
    padding: 10px 20px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.company-tagline p {
    margin: 0;
    font-size: 0.8rem;
    color: #e2e8f0;
    opacity: 0.9;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.bot-message {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1a365d;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
    border: 1px solid #e2e8f0;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.bot-message::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #f8fafc;
}

.user-message {
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 100%);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
    position: relative;
    box-shadow: 0 2px 8px rgba(222, 36, 39, 0.2);
}

.user-message::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #F6B402;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.6;
    margin-top: 5px;
    text-align: right;
}

.bot-message .message-time {
    text-align: left;
    color: #64748b;
}

.user-message .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.quick-actions {
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
}

.quick-action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.quick-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e0;
    color: #1a365d;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.quick-btn:hover {
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(222, 36, 39, 0.2);
}

.input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input-container {
    display: flex;
}

.data-entry-form {
    background: #f8f9fa;
    border: 2px solid #DE2427;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #DE2427;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #DE2427;
    box-shadow: 0 0 0 3px rgba(222, 36, 39, 0.1);
}

.form-group input:invalid {
    border-color: #dc3545;
}

.form-group .help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.form-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(222, 36, 39, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

#userInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    font-size: 16px;
    transition: border-color 0.3s;
}

#userInput:focus {
    border-color: #DE2427;
}

#sendButton {
    margin-left: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #DE2427 0%, #F6B402 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status-bar {
    background: #f5f5f5;
    padding: 10px 20px;
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.customer-details {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #4facfe;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-line;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.1);
    position: relative;
}

.customer-details::before {
    content: "📋";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
    position: relative;
}

.success-message::before {
    content: "✅";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.aadhaar-details {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 3px solid #0066cc;
    border-radius: 20px;
    padding: 0;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0, 102, 204, 0.2);
    position: relative;
    font-family: 'Poppins', sans-serif;
    overflow: hidden;
    max-width: 500px;
}

.aadhaar-card {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    border-radius: 17px;
    overflow: hidden;
}

.aadhaar-header {
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.uidai-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.uidai-logo img {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    padding: 5px;
}

.uidai-text {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.aadhaar-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    text-align: center;
}

.aadhaar-body {
    padding: 25px;
    color: #333;
}

.aadhaar-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.aadhaar-field:last-child {
    border-bottom: none;
}

.field-label {
    font-weight: 600;
    color: #0066cc;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 100px;
}

.field-value {
    font-weight: 500;
    color: #333;
    text-align: right;
    flex: 1;
    font-size: 0.9rem;
}

.aadhaar-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 2px;
    color: #0066cc;
}

.verification-badge {
    position: absolute;
    top: -8px;
    right: 15px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 6px 12px;
    font-size: 0.7rem;
    font-weight: bold;
    border-radius: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 5px;
}

.verification-badge::before {
    content: "✓";
    font-size: 0.8rem;
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
    position: relative;
}

.error-message::before {
    content: "❌";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.otp-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 8px 0;
    font-weight: bold;
    color: #856404;
    display: inline-block;
}

.restart-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.restart-hint.show {
    opacity: 1;
}

@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 95vh;
        border-radius: 10px;
    }
    
    .chat-header h1 {
        font-size: 1.5rem;
    }
    
    .message {
        max-width: 90%;
    }
}
