* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a365d 0%, #2d5a87 50%, #4a90a4 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #1a365d 0%, #2d5a87 100%);
    color: white;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.mbs-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.logo-container {
    position: relative;
}

.mbs-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 900;
    font-size: 18px;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.company-info h1 {
    font-size: 2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-info p {
    margin: 8px 0 0 0;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    color: #cbd5e0;
}

.service-description {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.service-description p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.85;
    font-style: italic;
    color: #e2e8f0;
}

.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.trust-item {
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.bot-message {
    background: #e3f2fd;
    color: #1565c0;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.user-message {
    background: linear-gradient(135deg, #1a365d 0%, #2d5a87 100%);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input-container {
    display: flex;
}

.data-entry-form {
    background: #f8f9fa;
    border: 2px solid #1a365d;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #1a365d;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #1a365d;
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

.form-group input:invalid {
    border-color: #dc3545;
}

.form-group .help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.form-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #1a365d 0%, #2d5a87 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(26, 54, 93, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

#userInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    font-size: 16px;
    transition: border-color 0.3s;
}

#userInput:focus {
    border-color: #1a365d;
}

#sendButton {
    margin-left: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #1a365d 0%, #2d5a87 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status-bar {
    background: #f5f5f5;
    padding: 10px 20px;
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.customer-details {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #4facfe;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-line;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.1);
    position: relative;
}

.customer-details::before {
    content: "📋";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
    position: relative;
}

.success-message::before {
    content: "✅";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.aadhaar-details {
    background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
    color: #1a365d;
    border: 2px solid #2d5a87;
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 6px 20px rgba(45, 90, 135, 0.15);
    position: relative;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-line;
}

.aadhaar-details::before {
    content: "🆔";
    position: absolute;
    top: -12px;
    left: 20px;
    background: white;
    padding: 0 10px;
    font-size: 1.4rem;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.aadhaar-details::after {
    content: "UIDAI Verified";
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 4px 12px;
    font-size: 0.7rem;
    font-weight: bold;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
    position: relative;
}

.error-message::before {
    content: "❌";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.otp-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 8px 0;
    font-weight: bold;
    color: #856404;
    display: inline-block;
}

.restart-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.restart-hint.show {
    opacity: 1;
}

@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 95vh;
        border-radius: 10px;
    }
    
    .chat-header h1 {
        font-size: 1.5rem;
    }
    
    .message {
        max-width: 90%;
    }
}
