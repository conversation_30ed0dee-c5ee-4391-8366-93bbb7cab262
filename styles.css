* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #004D9F 0%, #0066CC 100%);
    color: white;
    padding: 25px 20px;
    text-align: center;
    border-bottom: 3px solid #FFD700;
}

.mbs-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.mbs-logo img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
}

.company-info h1 {
    font-size: 1.8rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.company-info p {
    margin: 5px 0 0 0;
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 300;
}

.service-description {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.service-description p {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.8;
    font-style: italic;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.bot-message {
    background: #e3f2fd;
    color: #1565c0;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.user-message {
    background: linear-gradient(135deg, #004D9F 0%, #0066CC 100%);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input-container {
    display: flex;
}

.data-entry-form {
    background: #f8f9fa;
    border: 2px solid #004D9F;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #004D9F;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #004D9F;
    box-shadow: 0 0 0 3px rgba(0, 77, 159, 0.1);
}

.form-group input:invalid {
    border-color: #dc3545;
}

.form-group .help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.form-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #004D9F 0%, #0066CC 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 77, 159, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

#userInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    outline: none;
    font-size: 16px;
    transition: border-color 0.3s;
}

#userInput:focus {
    border-color: #004D9F;
}

#sendButton {
    margin-left: 10px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #004D9F 0%, #0066CC 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status-bar {
    background: #f5f5f5;
    padding: 10px 20px;
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.customer-details {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border: 2px solid #4facfe;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-line;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.1);
    position: relative;
}

.customer-details::before {
    content: "📋";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
    position: relative;
}

.success-message::before {
    content: "✅";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
    position: relative;
}

.error-message::before {
    content: "❌";
    position: absolute;
    top: -10px;
    left: 15px;
    background: white;
    padding: 0 8px;
    font-size: 1.2rem;
}

.otp-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 8px 0;
    font-weight: bold;
    color: #856404;
    display: inline-block;
}

.restart-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.restart-hint.show {
    opacity: 1;
}

@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 95vh;
        border-radius: 10px;
    }
    
    .chat-header h1 {
        font-size: 1.5rem;
    }
    
    .message {
        max-width: 90%;
    }
}
