<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reKYC Bot - Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #3a8bfe; }
        .demo-link {
            display: inline-block;
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
        }
        .demo-link:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 reKYC Bot - Test Suite</h1>
        <p>This page tests the bot logic and provides a link to the main application.</p>
        
        <a href="index.html" class="demo-link">🚀 Launch reKYC Bot Application</a>
        
        <h2>Automated Tests</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>Manual Test Scenarios</h2>
        <ol>
            <li><strong>Happy Path - No Address Update:</strong>
                <ul>
                    <li>Start → Customer ID: "CUST123" → Mobile: "9876543210" → OTP: [displayed] → Address Choice: "no" → Consent: "yes"</li>
                </ul>
            </li>
            <li><strong>Happy Path - With Address Update:</strong>
                <ul>
                    <li>Start → Customer ID: "CUST456" → Mobile: "8765432109" → OTP: [displayed] → Address Choice: "yes" → Aadhaar: "123456789012" → Aadhaar OTP: [displayed] → Consent: "yes"</li>
                </ul>
            </li>
            <li><strong>Error Scenarios:</strong>
                <ul>
                    <li>Invalid mobile numbers (less than 10 digits, starting with 0-5)</li>
                    <li>Invalid Aadhaar numbers (not 12 digits)</li>
                    <li>Wrong OTP entries</li>
                    <li>Process cancellation at final consent</li>
                </ul>
            </li>
        </ol>
    </div>

    <script src="bot.js"></script>
    <script>
        function runAllTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>Running Tests...</h3>';
            
            const tests = [
                testBotInitialization,
                testCustomerIdValidation,
                testMobileValidation,
                testOtpValidation,
                testAadhaarValidation,
                testStateTransitions,
                testCompleteWorkflow
            ];
            
            let passed = 0;
            let total = tests.length;
            
            tests.forEach((test, index) => {
                try {
                    const result = test();
                    if (result.success) {
                        passed++;
                        addTestResult(`✅ Test ${index + 1}: ${result.name}`, 'pass');
                    } else {
                        addTestResult(`❌ Test ${index + 1}: ${result.name} - ${result.error}`, 'fail');
                    }
                } catch (error) {
                    addTestResult(`❌ Test ${index + 1}: Error - ${error.message}`, 'fail');
                }
            });
            
            addTestResult(`\n📊 Results: ${passed}/${total} tests passed`, passed === total ? 'pass' : 'fail');
        }
        
        function addTestResult(message, type) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function testBotInitialization() {
            const bot = new ReKYCBot();
            return {
                success: bot.getCurrentState() === BotState.INIT,
                name: "Bot Initialization",
                error: bot.getCurrentState() !== BotState.INIT ? "Initial state incorrect" : null
            };
        }
        
        function testCustomerIdValidation() {
            const bot = new ReKYCBot();
            bot.processMessage('start');
            const response = bot.processMessage('CUST123');
            return {
                success: bot.getCurrentState() === BotState.MOBILE_INPUT && response.includes('mobile'),
                name: "Customer ID Validation",
                error: "Customer ID not accepted or state not updated"
            };
        }
        
        function testMobileValidation() {
            const bot = new ReKYCBot();
            bot.processMessage('start');
            bot.processMessage('CUST123');
            
            // Test invalid mobile
            let response1 = bot.processMessage('123');
            let test1 = response1.includes('Invalid') && bot.getCurrentState() === BotState.MOBILE_INPUT;
            
            // Test valid mobile
            let response2 = bot.processMessage('9876543210');
            let test2 = response2.includes('OTP') && bot.getCurrentState() === BotState.OTP_VALIDATION;
            
            return {
                success: test1 && test2,
                name: "Mobile Validation",
                error: !test1 ? "Invalid mobile accepted" : !test2 ? "Valid mobile rejected" : null
            };
        }
        
        function testOtpValidation() {
            const bot = new ReKYCBot();
            bot.processMessage('start');
            bot.processMessage('CUST123');
            bot.processMessage('9876543210');
            
            // Test wrong OTP
            let response1 = bot.processMessage('000000');
            let test1 = response1.includes('Invalid') && bot.getCurrentState() === BotState.OTP_VALIDATION;
            
            // Test correct OTP
            let response2 = bot.processMessage(bot.generatedOtp);
            let test2 = response2.includes('Customer Details') && bot.getCurrentState() === BotState.ADDRESS_CHOICE;
            
            return {
                success: test1 && test2,
                name: "OTP Validation",
                error: !test1 ? "Wrong OTP accepted" : !test2 ? "Correct OTP rejected" : null
            };
        }
        
        function testAadhaarValidation() {
            const bot = new ReKYCBot();
            // Setup to Aadhaar input state
            bot.processMessage('start');
            bot.processMessage('CUST123');
            bot.processMessage('9876543210');
            bot.processMessage(bot.generatedOtp);
            bot.processMessage('yes');
            
            // Test invalid Aadhaar
            let response1 = bot.processMessage('123');
            let test1 = response1.includes('Invalid') && bot.getCurrentState() === BotState.AADHAAR_INPUT;
            
            // Test valid Aadhaar
            let response2 = bot.processMessage('123456789012');
            let test2 = response2.includes('OTP') && bot.getCurrentState() === BotState.AADHAAR_OTP;
            
            return {
                success: test1 && test2,
                name: "Aadhaar Validation",
                error: !test1 ? "Invalid Aadhaar accepted" : !test2 ? "Valid Aadhaar rejected" : null
            };
        }
        
        function testStateTransitions() {
            const bot = new ReKYCBot();
            const expectedStates = [
                BotState.INIT,
                BotState.CUSTOMER_ID_INPUT,
                BotState.MOBILE_INPUT,
                BotState.OTP_VALIDATION,
                BotState.ADDRESS_CHOICE,
                BotState.FINAL_CONSENT,
                BotState.COMPLETED
            ];
            
            const inputs = ['start', 'CUST123', '9876543210', null, 'no', 'yes'];
            
            for (let i = 0; i < inputs.length; i++) {
                if (inputs[i] === null) {
                    inputs[i] = bot.generatedOtp; // Use generated OTP
                }
                if (inputs[i]) {
                    bot.processMessage(inputs[i]);
                }
                if (bot.getCurrentState() !== expectedStates[i + 1]) {
                    return {
                        success: false,
                        name: "State Transitions",
                        error: `Expected ${expectedStates[i + 1]}, got ${bot.getCurrentState()}`
                    };
                }
            }
            
            return {
                success: true,
                name: "State Transitions",
                error: null
            };
        }
        
        function testCompleteWorkflow() {
            const bot = new ReKYCBot();
            
            // Complete workflow with address update
            bot.processMessage('start');
            bot.processMessage('CUST123');
            bot.processMessage('9876543210');
            const otp1 = bot.generatedOtp;
            bot.processMessage(otp1);
            bot.processMessage('yes'); // Update address
            bot.processMessage('123456789012');
            const otp2 = bot.generatedAadhaarOtp;
            bot.processMessage(otp2);
            const response = bot.processMessage('yes');
            
            return {
                success: bot.getCurrentState() === BotState.COMPLETED && response.includes('completed successfully'),
                name: "Complete Workflow",
                error: "Workflow did not complete successfully"
            };
        }
    </script>
</body>
</html>
